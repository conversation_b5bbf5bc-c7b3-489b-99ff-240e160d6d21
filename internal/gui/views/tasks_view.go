// Package views provides the Tasks module UI with Material Design 3 and cyberpunk styling
package views

import (
	"fmt"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// TasksView represents the Tasks module interface
type TasksView struct {
	config    *config.Config
	theme     *cybertheme.CyberTheme
	container *container.Scroll
	content   fyne.CanvasObject

	// Task components
	taskList      *widget.List
	favoritesList *widget.List
	outputViewer  *widget.Entry
	commandEntry  *widget.Entry
	statusLabel   *widget.Label
	taskStatus    *widget.Label

	// Current state
	tasks        []Task
	favorites    []string
	history      []string
	currentTask  string
	selectedTask int
}

// Task represents a build task
type Task struct {
	Name        string
	Command     string
	Description string
	Type        string // makefile, taskfile, script
	LastRun     time.Time
	Status      string
	Duration    time.Duration
}

// NewTasksView creates a new Tasks module view
func NewTasksView(cfg *config.Config, theme *cybertheme.CyberTheme) (*TasksView, error) {
	view := &TasksView{
		config: cfg,
		theme:  theme,
		tasks: []Task{
			{Name: "build", Command: "go build -o bin/assistant cmd/assistant/main.go", Description: "Build the application", Type: "makefile", Status: "Ready", Duration: 2 * time.Second},
			{Name: "test", Command: "go test ./...", Description: "Run all tests", Type: "makefile", Status: "Ready", Duration: 5 * time.Second},
			{Name: "lint", Command: "golangci-lint run", Description: "Run linter", Type: "makefile", Status: "Ready", Duration: 3 * time.Second},
			{Name: "clean", Command: "rm -rf bin/ dist/", Description: "Clean build artifacts", Type: "makefile", Status: "Ready", Duration: 1 * time.Second},
			{Name: "docker-build", Command: "docker build -t assistant-go .", Description: "Build Docker image", Type: "taskfile", Status: "Ready", Duration: 30 * time.Second},
			{Name: "deploy", Command: "kubectl apply -f k8s/", Description: "Deploy to Kubernetes", Type: "taskfile", Status: "Ready", Duration: 10 * time.Second},
		},
		favorites: []string{"build", "test", "docker-build"},
		history:   []string{"build", "test", "lint", "build", "deploy"},
	}

	// Create the main content
	content := view.createTasksContent()

	// Create scrollable container
	scrollContainer := container.NewScroll(content)
	scrollContainer.SetMinSize(fyne.NewSize(900, 650))

	view.container = scrollContainer
	view.content = content

	return view, nil
}

// createTasksContent creates the main Tasks interface
func (tv *TasksView) createTasksContent() fyne.CanvasObject {
	// Create header
	header := widget.NewRichTextFromMarkdown(`
# 📋 Task Management Module

**Makefile & Taskfile Integration**

---
`)

	// Create task browser section
	taskSection := tv.createTaskSection()

	// Create execution section
	executionSection := tv.createExecutionSection()

	// Create output section
	outputSection := tv.createOutputSection()

	// Create status section
	statusSection := tv.createStatusSection()

	// Combine all sections with proper spacing
	return container.NewVBox(
		header,
		widget.NewSeparator(),
		taskSection,
		widget.NewSeparator(),
		executionSection,
		widget.NewSeparator(),
		outputSection,
		widget.NewSeparator(),
		statusSection,
	)
}

// createTaskSection creates the task browser interface
func (tv *TasksView) createTaskSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Available Tasks")
	sectionHeader.TextStyle.Bold = true

	// Task list
	tv.taskList = widget.NewList(
		func() int { return len(tv.tasks) },
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("Task"),
				widget.NewLabel("Type"),
				widget.NewLabel("Status"),
				widget.NewLabel("Duration"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			task := tv.tasks[id]
			hbox := obj.(*fyne.Container)

			// Status icon
			statusIcon := "🟢"
			if task.Status == "Running" {
				statusIcon = "🟡"
			} else if task.Status == "Failed" {
				statusIcon = "🔴"
			}

			// Update labels
			hbox.Objects[0].(*widget.Label).SetText(fmt.Sprintf("%s - %s", task.Name, task.Description))
			hbox.Objects[1].(*widget.Label).SetText(strings.ToUpper(task.Type))
			hbox.Objects[2].(*widget.Label).SetText(fmt.Sprintf("%s %s", statusIcon, task.Status))
			hbox.Objects[3].(*widget.Label).SetText(task.Duration.String())
		},
	)
	tv.taskList.Resize(fyne.NewSize(800, 150))

	// Set up selection handling
	tv.taskList.OnSelected = func(id widget.ListItemID) {
		tv.selectedTask = id
	}

	// Favorites section
	favoritesHeader := widget.NewLabel("Favorite Tasks")
	favoritesHeader.TextStyle.Bold = true

	tv.favoritesList = widget.NewList(
		func() int { return len(tv.favorites) },
		func() fyne.CanvasObject { return widget.NewLabel("Favorite") },
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id < len(tv.favorites) {
				favName := tv.favorites[id]
				// Find the task details
				for _, task := range tv.tasks {
					if task.Name == favName {
						obj.(*widget.Label).SetText(fmt.Sprintf("⭐ %s - %s", task.Name, task.Description))
						break
					}
				}
			}
		},
	)
	tv.favoritesList.Resize(fyne.NewSize(400, 100))

	// Task action buttons
	runBtn := createEnhancedButton("Run Task", func() {
		tv.runSelectedTask()
	}, tv.theme, true)

	addFavBtn := createEnhancedButton("Add to Favorites", func() {
		tv.addToFavorites()
	}, tv.theme, false)

	refreshBtn := createEnhancedButton("Refresh Tasks", func() {
		tv.refreshTasks()
	}, tv.theme, false)

	taskButtons := container.NewHBox(runBtn, addFavBtn, refreshBtn)

	// Combine task browser elements
	taskBrowser := container.NewHBox(
		container.NewVBox(sectionHeader, tv.taskList, taskButtons),
		container.NewVBox(favoritesHeader, tv.favoritesList),
	)

	return taskBrowser
}

// createExecutionSection creates the task execution interface
func (tv *TasksView) createExecutionSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Task Execution")
	sectionHeader.TextStyle.Bold = true

	// Command entry
	commandLabel := widget.NewLabel("Custom Command:")
	commandLabel.TextStyle.Bold = true

	tv.commandEntry = widget.NewEntry()
	tv.commandEntry.SetPlaceHolder("Enter custom command or select a task above...")
	tv.commandEntry.Resize(fyne.NewSize(600, 40))

	// Execution buttons
	executeBtn := createEnhancedButton("Execute", func() {
		tv.executeCustomCommand()
	}, tv.theme, true)

	stopBtn := createEnhancedButton("Stop", func() {
		tv.stopExecution()
	}, tv.theme, false)

	clearBtn := createEnhancedButton("Clear Output", func() {
		tv.clearOutput()
	}, tv.theme, false)

	executionButtons := container.NewHBox(executeBtn, stopBtn, clearBtn)

	// Task status
	tv.taskStatus = widget.NewLabel("No task running")
	tv.taskStatus.TextStyle.Bold = true

	return container.NewVBox(
		sectionHeader,
		commandLabel,
		tv.commandEntry,
		executionButtons,
		tv.taskStatus,
	)
}

// createOutputSection creates the output display interface
func (tv *TasksView) createOutputSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Task Output")
	sectionHeader.TextStyle.Bold = true

	// Output viewer
	tv.outputViewer = widget.NewMultiLineEntry()
	tv.outputViewer.SetText(`>>> TASK EXECUTION OUTPUT <<<

$ go build -o bin/assistant cmd/assistant/main.go
Building application...
✅ Build completed successfully in 2.1s

$ go test ./...
Running tests...
✅ All tests passed (15 tests, 0 failures)

$ golangci-lint run
Running linter...
✅ No issues found

>>> READY FOR NEXT TASK <<<`)
	tv.outputViewer.Resize(fyne.NewSize(800, 200))

	// Output controls
	saveLogBtn := createEnhancedButton("Save Log", func() {
		tv.saveOutput()
	}, tv.theme, false)

	copyBtn := createEnhancedButton("Copy Output", func() {
		tv.copyOutput()
	}, tv.theme, false)

	outputControls := container.NewHBox(saveLogBtn, copyBtn)

	return container.NewVBox(
		sectionHeader,
		tv.outputViewer,
		outputControls,
	)
}

// createStatusSection creates the status display
func (tv *TasksView) createStatusSection() fyne.CanvasObject {
	tv.statusLabel = widget.NewLabel(">>> TASK MODULE READY <<<")
	tv.statusLabel.Alignment = fyne.TextAlignCenter
	tv.statusLabel.TextStyle.Bold = true

	return tv.statusLabel
}

// Task operation methods
func (tv *TasksView) runSelectedTask() {
	if tv.selectedTask >= 0 && tv.selectedTask < len(tv.tasks) {
		task := tv.tasks[tv.selectedTask]
		tv.currentTask = task.Name
		tv.taskStatus.SetText(fmt.Sprintf("Running: %s", task.Name))
		tv.statusLabel.SetText(">>> EXECUTING TASK <<<")
		tv.outputViewer.SetText(fmt.Sprintf(">>> EXECUTING: %s <<<\n\n$ %s\n\nTask started...", task.Name, task.Command))

		// Simulate task execution
		go tv.simulateTaskExecution(task)
	}
}

func (tv *TasksView) simulateTaskExecution(task Task) {
	time.Sleep(1 * time.Second)
	tv.outputViewer.SetText(tv.outputViewer.Text + "\n✅ Task completed successfully")
	tv.taskStatus.SetText("Task completed")
	tv.statusLabel.SetText(">>> TASK COMPLETED <<<")
}

func (tv *TasksView) executeCustomCommand() {
	command := strings.TrimSpace(tv.commandEntry.Text)
	if command != "" {
		tv.statusLabel.SetText(">>> EXECUTING CUSTOM COMMAND <<<")
		tv.outputViewer.SetText(fmt.Sprintf(">>> CUSTOM COMMAND <<<\n\n$ %s\n\nExecuting...", command))
		tv.taskStatus.SetText("Running custom command")
	}
}

func (tv *TasksView) addToFavorites() {
	if tv.selectedTask >= 0 && tv.selectedTask < len(tv.tasks) {
		task := tv.tasks[tv.selectedTask]
		tv.favorites = append(tv.favorites, task.Name)
		tv.favoritesList.Refresh()
		tv.statusLabel.SetText(">>> ADDED TO FAVORITES <<<")
	}
}

func (tv *TasksView) refreshTasks() {
	tv.statusLabel.SetText(">>> REFRESHING TASKS <<<")
	tv.taskList.Refresh()
	tv.statusLabel.SetText(">>> TASKS REFRESHED <<<")
}

func (tv *TasksView) stopExecution() {
	tv.statusLabel.SetText(">>> STOPPING EXECUTION <<<")
	tv.taskStatus.SetText("Execution stopped")
}

func (tv *TasksView) clearOutput() {
	tv.outputViewer.SetText(">>> OUTPUT CLEARED <<<")
	tv.statusLabel.SetText(">>> OUTPUT CLEARED <<<")
}

func (tv *TasksView) saveOutput() {
	tv.statusLabel.SetText(">>> SAVING OUTPUT LOG <<<")
	// TODO: Implement save functionality
}

func (tv *TasksView) copyOutput() {
	tv.statusLabel.SetText(">>> OUTPUT COPIED TO CLIPBOARD <<<")
	// TODO: Implement copy functionality
}

// Content returns the Tasks view's content
func (tv *TasksView) Content() fyne.CanvasObject {
	return tv.container
}

// Refresh refreshes the Tasks view
func (tv *TasksView) Refresh() {
	if tv.container != nil {
		tv.container.Refresh()
	}
}
