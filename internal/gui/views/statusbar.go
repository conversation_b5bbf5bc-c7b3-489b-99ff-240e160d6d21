// Package views provides the status bar component
// Implements cyberpunk-styled status display with real-time updates
package views

import (
	"fmt"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// StatusBar represents the application status bar
type StatusBar struct {
	config    *config.Config
	theme     *cybertheme.CyberTheme
	container *fyne.Container

	// Status components
	messageLabel *widget.Label
	timeLabel    *widget.Label
	statusLabel  *widget.Label

	// Current status
	currentMessage string
	currentLevel   string
}

// NewStatusBar creates a new status bar with cyberpunk styling
func NewStatusBar(cfg *config.Config, theme *cybertheme.CyberTheme) (*StatusBar, error) {
	statusBar := &StatusBar{
		config: cfg,
		theme:  theme,
	}

	// Initialize components
	statusBar.initializeComponents()

	// Start time updater
	go statusBar.updateTime()

	return statusBar, nil
}

// initializeCom<PERSON> creates enhanced status bar components with Material Design 3 styling
func (sb *StatusBar) initializeComponents() {
	// Create enhanced message label (left side) with better typography
	sb.messageLabel = widget.NewLabel(">>> ENHANCED SYSTEM READY <<<")
	sb.messageLabel.TextStyle.Bold = true

	// Create enhanced status indicator (center) with cyberpunk styling
	sb.statusLabel = widget.NewLabel("🟢 ONLINE")
	sb.statusLabel.TextStyle.Bold = true

	// Create enhanced time label (right side) with monospace styling
	sb.timeLabel = widget.NewLabel(time.Now().Format("15:04:05"))
	sb.timeLabel.TextStyle.Monospace = true

	// Create enhanced separator
	separator := widget.NewSeparator()

	// Create container with improved border layout and spacing
	sb.container = container.NewBorder(
		nil, nil, // top, bottom
		sb.messageLabel, // left
		container.NewHBox( // right
			sb.statusLabel,
			separator,
			sb.timeLabel,
		),
		nil, // center (empty)
	)

	// Set enhanced styling
	sb.applyTheme()
}

// applyTheme applies cyberpunk styling to status bar components
func (sb *StatusBar) applyTheme() {
	// Apply cyberpunk colors to labels
	// Note: Fyne doesn't directly support label colors, but we can use rich text
	// This is a simplified implementation - full version would use rich text widgets
}

// updateTime continuously updates the time display
func (sb *StatusBar) updateTime() {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		currentTime := time.Now().Format("15:04:05")
		sb.timeLabel.SetText(currentTime)
	}
}

// SetMessage updates the status message with appropriate styling
func (sb *StatusBar) SetMessage(message, level string) {
	sb.currentMessage = message
	sb.currentLevel = level

	// Update message label
	sb.messageLabel.SetText(message)

	// Update status indicator based on level
	sb.updateStatusIndicator(level)

	// Refresh the container
	sb.container.Refresh()
}

// updateStatusIndicator updates the status indicator based on level
func (sb *StatusBar) updateStatusIndicator(level string) {
	var indicator string

	switch level {
	case "success":
		indicator = "🟢 SUCCESS"
	case "warning":
		indicator = "🟡 WARNING"
	case "error":
		indicator = "🔴 ERROR"
	case "info":
		indicator = "🔵 INFO"
	case "processing":
		indicator = "🟣 PROCESSING"
	default:
		indicator = "🟢 ONLINE"
	}

	sb.statusLabel.SetText(indicator)
}

// SetProgress shows a progress indicator in the status bar
func (sb *StatusBar) SetProgress(message string, progress float64) {
	progressText := fmt.Sprintf("%s [%.0f%%]", message, progress*100)
	sb.SetMessage(progressText, "processing")
}

// ShowTemporaryMessage shows a message for a specific duration
func (sb *StatusBar) ShowTemporaryMessage(message, level string, duration time.Duration) {
	// Store current message
	originalMessage := sb.currentMessage
	originalLevel := sb.currentLevel

	// Show temporary message
	sb.SetMessage(message, level)

	// Restore original message after duration
	go func() {
		time.Sleep(duration)
		sb.SetMessage(originalMessage, originalLevel)
	}()
}

// SetConnectionStatus updates connection status for a specific service
func (sb *StatusBar) SetConnectionStatus(service string, connected bool) {
	var status string
	var level string

	if connected {
		status = fmt.Sprintf(">>> %s CONNECTED <<<", service)
		level = "success"
	} else {
		status = fmt.Sprintf(">>> %s DISCONNECTED <<<", service)
		level = "warning"
	}

	sb.SetMessage(status, level)
}

// ShowSystemInfo displays system information
func (sb *StatusBar) ShowSystemInfo() {
	info := fmt.Sprintf(">>> %s v%s | %s <<<",
		sb.config.App.Name,
		sb.config.App.Version,
		sb.config.App.Environment)
	sb.SetMessage(info, "info")
}

// ShowModuleStatus displays module-specific status
func (sb *StatusBar) ShowModuleStatus(moduleName, status string) {
	message := fmt.Sprintf(">>> MODULE %s: %s <<<", moduleName, status)
	sb.SetMessage(message, "info")
}

// Content returns the status bar's content
func (sb *StatusBar) Content() fyne.CanvasObject {
	return sb.container
}

// Refresh refreshes the status bar
func (sb *StatusBar) Refresh() {
	if sb.container != nil {
		sb.container.Refresh()
	}
}

// GetCurrentMessage returns the current status message
func (sb *StatusBar) GetCurrentMessage() string {
	return sb.currentMessage
}

// GetCurrentLevel returns the current status level
func (sb *StatusBar) GetCurrentLevel() string {
	return sb.currentLevel
}

// SetTheme updates the status bar theme
func (sb *StatusBar) SetTheme(theme *cybertheme.CyberTheme) {
	sb.theme = theme
	sb.applyTheme()
	sb.Refresh()
}

// ShowError displays an error message
func (sb *StatusBar) ShowError(err error) {
	message := fmt.Sprintf(">>> ERROR: %s <<<", err.Error())
	sb.SetMessage(message, "error")
}

// ShowSuccess displays a success message
func (sb *StatusBar) ShowSuccess(message string) {
	successMsg := fmt.Sprintf(">>> SUCCESS: %s <<<", message)
	sb.SetMessage(successMsg, "success")
}

// ShowWarning displays a warning message
func (sb *StatusBar) ShowWarning(message string) {
	warningMsg := fmt.Sprintf(">>> WARNING: %s <<<", message)
	sb.SetMessage(warningMsg, "warning")
}

// ShowInfo displays an info message
func (sb *StatusBar) ShowInfo(message string) {
	infoMsg := fmt.Sprintf(">>> INFO: %s <<<", message)
	sb.SetMessage(infoMsg, "info")
}

// StartProgressAnimation starts an animated progress indicator
func (sb *StatusBar) StartProgressAnimation(message string) chan bool {
	stopChan := make(chan bool)

	go func() {
		frames := []string{"⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"}
		frameIndex := 0

		ticker := time.NewTicker(100 * time.Millisecond)
		defer ticker.Stop()

		for {
			select {
			case <-stopChan:
				return
			case <-ticker.C:
				animatedMessage := fmt.Sprintf("%s %s", frames[frameIndex], message)
				sb.SetMessage(animatedMessage, "processing")
				frameIndex = (frameIndex + 1) % len(frames)
			}
		}
	}()

	return stopChan
}

// StopProgressAnimation stops the progress animation
func (sb *StatusBar) StopProgressAnimation(stopChan chan bool, finalMessage, level string) {
	close(stopChan)
	sb.SetMessage(finalMessage, level)
}
