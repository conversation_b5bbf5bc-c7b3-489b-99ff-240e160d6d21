// Package views provides the MCP module UI with Material Design 3 and cyberpunk styling
package views

import (
	"fmt"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// MCPView represents the MCP (Model Context Protocol) module interface
type MCPView struct {
	config    *config.Config
	theme     *cybertheme.CyberTheme
	container *container.Scroll
	content   fyne.CanvasObject

	// MCP components
	connectionList *widget.List
	toolList       *widget.List
	contextViewer  *widget.Entry
	messageList    *widget.List
	statusLabel    *widget.Label
	connectionStatus *widget.Label

	// Current state
	connections        []MCPConnection
	tools              []MCPTool
	contexts           []MCPContext
	messages           []MCPMessage
	currentConnection  string
	selectedConnection int
	selectedTool       int
}

// MCPConnection represents an MCP server connection
type MCPConnection struct {
	Name        string
	URL         string
	Status      string
	Protocol    string
	LastPing    time.Time
	Tools       int
	Contexts    int
}

// MCPTool represents an MCP tool
type MCPTool struct {
	Name        string
	Description string
	Schema      string
	Parameters  []string
	LastUsed    time.Time
}

// MCPContext represents an MCP context
type MCPContext struct {
	ID          string
	Name        string
	Type        string
	Size        int
	LastUpdated time.Time
}

// MCPMessage represents an MCP message
type MCPMessage struct {
	ID        string
	Type      string
	Content   string
	Timestamp time.Time
	Status    string
}

// NewMCPView creates a new MCP module view
func NewMCPView(cfg *config.Config, theme *cybertheme.CyberTheme) (*MCPView, error) {
	view := &MCPView{
		config: cfg,
		theme:  theme,
		connections: []MCPConnection{
			{Name: "Claude AI", URL: "mcp://claude.anthropic.com", Status: "Connected", Protocol: "MCP/1.0", Tools: 15, Contexts: 3},
			{Name: "Local Assistant", URL: "mcp://localhost:8080", Status: "Disconnected", Protocol: "MCP/1.0", Tools: 8, Contexts: 1},
			{Name: "Development Server", URL: "mcp://dev.example.com", Status: "Connected", Protocol: "MCP/1.0", Tools: 12, Contexts: 5},
		},
		tools: []MCPTool{
			{Name: "file_reader", Description: "Read and analyze files", Schema: "file_path: string", Parameters: []string{"file_path", "encoding"}},
			{Name: "code_analyzer", Description: "Analyze code structure", Schema: "code: string, language: string", Parameters: []string{"code", "language", "depth"}},
			{Name: "web_search", Description: "Search the web for information", Schema: "query: string", Parameters: []string{"query", "max_results"}},
			{Name: "database_query", Description: "Execute database queries", Schema: "sql: string", Parameters: []string{"sql", "database"}},
		},
		contexts: []MCPContext{
			{ID: "ctx_001", Name: "Current Project", Type: "codebase", Size: 1024, LastUpdated: time.Now()},
			{ID: "ctx_002", Name: "Documentation", Type: "knowledge", Size: 512, LastUpdated: time.Now().Add(-1 * time.Hour)},
			{ID: "ctx_003", Name: "Chat History", Type: "conversation", Size: 256, LastUpdated: time.Now().Add(-30 * time.Minute)},
		},
		messages: []MCPMessage{
			{ID: "msg_001", Type: "tool_call", Content: "file_reader called with path: /src/main.go", Timestamp: time.Now(), Status: "completed"},
			{ID: "msg_002", Type: "response", Content: "File analyzed successfully. Found 3 functions and 2 structs.", Timestamp: time.Now().Add(-1 * time.Minute), Status: "completed"},
			{ID: "msg_003", Type: "context_update", Content: "Added new context: Current Project", Timestamp: time.Now().Add(-5 * time.Minute), Status: "completed"},
		},
	}

	// Create the main content
	content := view.createMCPContent()

	// Create scrollable container
	scrollContainer := container.NewScroll(content)
	scrollContainer.SetMinSize(fyne.NewSize(900, 650))

	view.container = scrollContainer
	view.content = content

	return view, nil
}

// createMCPContent creates the main MCP interface
func (mv *MCPView) createMCPContent() fyne.CanvasObject {
	// Create header
	header := widget.NewRichTextFromMarkdown(`
# 🤖 MCP Management Module

**Model Context Protocol Interface**

---
`)

	// Create connection management section
	connectionSection := mv.createConnectionSection()

	// Create tools section
	toolsSection := mv.createToolsSection()

	// Create context section
	contextSection := mv.createContextSection()

	// Create message section
	messageSection := mv.createMessageSection()

	// Create status section
	statusSection := mv.createStatusSection()

	// Combine all sections with proper spacing
	return container.NewVBox(
		header,
		widget.NewSeparator(),
		connectionSection,
		widget.NewSeparator(),
		toolsSection,
		widget.NewSeparator(),
		contextSection,
		widget.NewSeparator(),
		messageSection,
		widget.NewSeparator(),
		statusSection,
	)
}

// createConnectionSection creates the MCP connection management interface
func (mv *MCPView) createConnectionSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("MCP Connections")
	sectionHeader.TextStyle.Bold = true

	// Connection list
	mv.connectionList = widget.NewList(
		func() int { return len(mv.connections) },
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("Connection"),
				widget.NewLabel("Status"),
				widget.NewLabel("Tools"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			conn := mv.connections[id]
			hbox := obj.(*fyne.Container)

			// Status icon
			statusIcon := "🟢"
			if conn.Status != "Connected" {
				statusIcon = "🔴"
			}

			// Update labels
			hbox.Objects[0].(*widget.Label).SetText(fmt.Sprintf("%s (%s)", conn.Name, conn.Protocol))
			hbox.Objects[1].(*widget.Label).SetText(fmt.Sprintf("%s %s", statusIcon, conn.Status))
			hbox.Objects[2].(*widget.Label).SetText(fmt.Sprintf("Tools: %d, Contexts: %d", conn.Tools, conn.Contexts))
		},
	)
	mv.connectionList.Resize(fyne.NewSize(600, 120))

	// Set up selection handling
	mv.connectionList.OnSelected = func(id widget.ListItemID) {
		mv.selectedConnection = id
	}

	// Connection buttons
	connectBtn := createEnhancedButton("Connect", func() {
		mv.connectToMCP()
	}, mv.theme, true)

	disconnectBtn := createEnhancedButton("Disconnect", func() {
		mv.disconnectFromMCP()
	}, mv.theme, false)

	addBtn := createEnhancedButton("Add Connection", func() {
		mv.showAddConnectionDialog()
	}, mv.theme, false)

	connectionButtons := container.NewHBox(connectBtn, disconnectBtn, addBtn)

	// Connection status
	mv.connectionStatus = widget.NewLabel("Select a connection to manage")
	mv.connectionStatus.TextStyle.Bold = true

	return container.NewVBox(
		sectionHeader,
		mv.connectionList,
		connectionButtons,
		mv.connectionStatus,
	)
}

// createToolsSection creates the MCP tools interface
func (mv *MCPView) createToolsSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Available Tools")
	sectionHeader.TextStyle.Bold = true

	// Tools list
	mv.toolList = widget.NewList(
		func() int { return len(mv.tools) },
		func() fyne.CanvasObject {
			return container.NewVBox(
				widget.NewLabel("Tool"),
				widget.NewLabel("Description"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			tool := mv.tools[id]
			vbox := obj.(*fyne.Container)

			// Update labels
			vbox.Objects[0].(*widget.Label).SetText(fmt.Sprintf("🛠️ %s", tool.Name))
			vbox.Objects[0].(*widget.Label).TextStyle.Bold = true
			vbox.Objects[1].(*widget.Label).SetText(tool.Description)
		},
	)
	mv.toolList.Resize(fyne.NewSize(400, 150))

	// Set up selection handling
	mv.toolList.OnSelected = func(id widget.ListItemID) {
		mv.selectedTool = id
	}

	// Tool action buttons
	callBtn := createEnhancedButton("Call Tool", func() {
		mv.callSelectedTool()
	}, mv.theme, true)

	schemaBtn := createEnhancedButton("View Schema", func() {
		mv.viewToolSchema()
	}, mv.theme, false)

	refreshBtn := createEnhancedButton("Refresh Tools", func() {
		mv.refreshTools()
	}, mv.theme, false)

	toolButtons := container.NewHBox(callBtn, schemaBtn, refreshBtn)

	return container.NewVBox(
		sectionHeader,
		mv.toolList,
		toolButtons,
	)
}

// createContextSection creates the context management interface
func (mv *MCPView) createContextSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Context Management")
	sectionHeader.TextStyle.Bold = true

	// Context viewer
	mv.contextViewer = widget.NewMultiLineEntry()
	mv.contextViewer.SetText(`>>> ACTIVE CONTEXTS <<<

📁 Current Project (ctx_001)
   Type: codebase
   Size: 1024 tokens
   Last Updated: 2024-01-20 10:30:15

📚 Documentation (ctx_002)
   Type: knowledge
   Size: 512 tokens
   Last Updated: 2024-01-20 09:30:15

💬 Chat History (ctx_003)
   Type: conversation
   Size: 256 tokens
   Last Updated: 2024-01-20 10:00:15

>>> CONTEXT READY <<<`)
	mv.contextViewer.Resize(fyne.NewSize(600, 150))

	// Context buttons
	addContextBtn := createEnhancedButton("Add Context", func() {
		mv.addContext()
	}, mv.theme, true)

	clearBtn := createEnhancedButton("Clear Context", func() {
		mv.clearContext()
	}, mv.theme, false)

	contextButtons := container.NewHBox(addContextBtn, clearBtn)

	return container.NewVBox(
		sectionHeader,
		mv.contextViewer,
		contextButtons,
	)
}

// createMessageSection creates the message history interface
func (mv *MCPView) createMessageSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Message History")
	sectionHeader.TextStyle.Bold = true

	// Message list
	mv.messageList = widget.NewList(
		func() int { return len(mv.messages) },
		func() fyne.CanvasObject {
			return container.NewVBox(
				widget.NewLabel("Message"),
				widget.NewLabel("Content"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			msg := mv.messages[id]
			container := obj.(*container.Container)

			// Type icon
			typeIcon := "📨"
			if msg.Type == "tool_call" {
				typeIcon = "🛠️"
			} else if msg.Type == "response" {
				typeIcon = "💬"
			} else if msg.Type == "context_update" {
				typeIcon = "📁"
			}

			// Update labels
			container.Objects[0].(*widget.Label).SetText(fmt.Sprintf("%s %s [%s]", typeIcon, strings.ToUpper(msg.Type), msg.Timestamp.Format("15:04:05")))
			container.Objects[0].(*widget.Label).TextStyle.Bold = true
			container.Objects[1].(*widget.Label).SetText(msg.Content)
		},
	)
	mv.messageList.Resize(fyne.NewSize(700, 120))

	return container.NewVBox(
		sectionHeader,
		mv.messageList,
	)
}

// createStatusSection creates the status display
func (mv *MCPView) createStatusSection() fyne.CanvasObject {
	mv.statusLabel = widget.NewLabel(">>> MCP MODULE READY <<<")
	mv.statusLabel.Alignment = fyne.TextAlignCenter
	mv.statusLabel.TextStyle.Bold = true

	return mv.statusLabel
}

// MCP operation methods
func (mv *MCPView) connectToMCP() {
	if mv.connectionList.GetSelected() >= 0 {
		selected := mv.connectionList.GetSelected()
		conn := mv.connections[selected]
		mv.connections[selected].Status = "Connected"
		mv.currentConnection = conn.Name
		mv.connectionStatus.SetText(fmt.Sprintf("Connected to: %s", conn.Name))
		mv.statusLabel.SetText(">>> MCP CONNECTION ESTABLISHED <<<")
		mv.connectionList.Refresh()
	}
}

func (mv *MCPView) disconnectFromMCP() {
	if mv.connectionList.GetSelected() >= 0 {
		selected := mv.connectionList.GetSelected()
		mv.connections[selected].Status = "Disconnected"
		mv.connectionStatus.SetText("No active connections")
		mv.statusLabel.SetText(">>> MCP CONNECTION CLOSED <<<")
		mv.connectionList.Refresh()
	}
}

func (mv *MCPView) callSelectedTool() {
	if mv.toolList.GetSelected() >= 0 {
		selected := mv.toolList.GetSelected()
		tool := mv.tools[selected]
		mv.statusLabel.SetText(fmt.Sprintf(">>> CALLING TOOL: %s <<<", strings.ToUpper(tool.Name)))
		// TODO: Implement tool calling functionality
	}
}

func (mv *MCPView) viewToolSchema() {
	if mv.toolList.GetSelected() >= 0 {
		selected := mv.toolList.GetSelected()
		tool := mv.tools[selected]
		mv.statusLabel.SetText(fmt.Sprintf(">>> VIEWING SCHEMA: %s <<<", strings.ToUpper(tool.Name)))
		// TODO: Implement schema viewing functionality
	}
}

func (mv *MCPView) refreshTools() {
	mv.statusLabel.SetText(">>> REFRESHING TOOLS <<<")
	mv.toolList.Refresh()
	mv.statusLabel.SetText(">>> TOOLS REFRESHED <<<")
}

func (mv *MCPView) addContext() {
	mv.statusLabel.SetText(">>> ADDING CONTEXT <<<")
	// TODO: Implement add context functionality
}

func (mv *MCPView) clearContext() {
	mv.statusLabel.SetText(">>> CLEARING CONTEXT <<<")
	mv.contextViewer.SetText(">>> CONTEXT CLEARED <<<")
}

func (mv *MCPView) showAddConnectionDialog() {
	mv.statusLabel.SetText(">>> ADD CONNECTION DIALOG <<<")
	// TODO: Implement add connection dialog
}

// Content returns the MCP view's content
func (mv *MCPView) Content() fyne.CanvasObject {
	return mv.container
}

// Refresh refreshes the MCP view
func (mv *MCPView) Refresh() {
	if mv.container != nil {
		mv.container.Refresh()
	}
}
