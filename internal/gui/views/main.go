// Package views provides GUI view components for Assistant-Go
// Implements the cyberpunk aesthetic with proper scrolling and responsive design
package views

import (
	"strings"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// MainView represents the main content area of the application
type MainView struct {
	config    *config.Config
	theme     *cybertheme.CyberTheme
	container *container.Scroll
	content   fyne.CanvasObject
}

// NewMainView creates a new main view with cyberpunk styling
func NewMainView(cfg *config.Config, theme *cybertheme.CyberTheme) (*MainView, error) {
	// Create the main content container with scrolling support
	// This fixes the content overflow issues mentioned in requirements
	mainContainer := container.NewVBox()

	// Create scrollable container to handle content overflow with better sizing
	scrollContainer := container.NewScroll(mainContainer)
	scrollContainer.SetMinSize(fyne.NewSize(900, 650)) // Increased size for better content display

	// Create welcome content with cyberpunk styling
	welcomeContent := createWelcomeContent(cfg, theme)
	mainContainer.Add(welcomeContent)

	return &MainView{
		config:    cfg,
		theme:     theme,
		container: scrollContainer,
		content:   welcomeContent,
	}, nil
}

// createWelcomeContent creates the initial welcome screen
func createWelcomeContent(cfg *config.Config, theme *cybertheme.CyberTheme) fyne.CanvasObject {
	// Create cyberpunk-styled welcome message
	welcomeText := widget.NewRichTextFromMarkdown(`
# 🖥️ Assistant-Go Development Environment

**STATUS:** SYSTEM ONLINE
**VERSION:** ` + cfg.App.Version + `
**ENVIRONMENT:** ` + cfg.App.Environment + `

> Welcome to your cyberpunk development assistant.
> All systems are operational and ready for your commands.

## Available Modules:

### 🗄️ Database Module
- **PostgreSQL Navigator** - Intelligent query optimization and visual explain plans
- **Connection Management** - Multiple database connections with auto-pooling
- **AI-Powered Analysis** - Smart error explanations and performance suggestions

### ☸️ Kubernetes Module
- **Cluster Commander** - Visual cluster management and real-time monitoring
- **Resource Browser** - Intuitive navigation of namespaces, pods, and services
- **Deployment Tools** - Drag-and-drop resource management

### 📋 Task Module
- **Makefile Integration** - Direct access to your build tasks
- **Taskfile Support** - Modern task runner integration
- **Favorites Management** - Quick access to frequently used commands

### 🤖 MCP Module (Future)
- **Model Context Protocol** - Visualize AI interactions in real-time
- **Context Flow** - Debug and understand AI tool calls
- **Response Analysis** - Monitor model responses and context usage

## Getting Started:

1. **Configure Connections** - Set up your database and Kubernetes connections
2. **Select a Module** - Choose from the sidebar to begin working
3. **Explore Features** - Each module provides comprehensive development tools

---

**>>> READY FOR DEVELOPMENT <<<**
`)

	// Create enhanced status indicators with better spacing
	statusContainer := container.NewHBox(
		createEnhancedStatusIndicator("Database", "offline", theme),
		widget.NewLabel("    "), // Spacer for better separation
		createEnhancedStatusIndicator("Kubernetes", "offline", theme),
		widget.NewLabel("    "), // Spacer for better separation
		createEnhancedStatusIndicator("Tasks", "online", theme),
		widget.NewLabel("    "), // Spacer for better separation
		createEnhancedStatusIndicator("MCP", "pending", theme),
	)

	// Create enhanced action buttons with optimal spacing
	actionContainer := container.NewHBox(
		createEnhancedButton("Configure Database", func() {
			// TODO: Open database configuration
		}, theme, true),
		createEnhancedButton("Setup Kubernetes", func() {
			// TODO: Open K8s configuration
		}, theme, false),
		createEnhancedButton("View Tasks", func() {
			// TODO: Switch to tasks module
		}, theme, false),
	)

	// Create enhanced separators
	separator1 := widget.NewSeparator()
	separator2 := widget.NewSeparator()
	separator3 := widget.NewSeparator()

	// Create section headers with enhanced typography
	statusHeader := widget.NewLabel("System Status:")
	statusHeader.TextStyle.Bold = true

	actionsHeader := widget.NewLabel("Quick Actions:")
	actionsHeader.TextStyle.Bold = true

	footerLabel := widget.NewLabel(">>> ENHANCED CYBERPUNK DEVELOPMENT ENVIRONMENT ACTIVE <<<")
	footerLabel.Alignment = fyne.TextAlignCenter

	// Add spacing widgets for better layout
	spacer1 := widget.NewLabel("") // Vertical spacer
	spacer2 := widget.NewLabel("") // Vertical spacer
	spacer3 := widget.NewLabel("") // Vertical spacer
	spacer4 := widget.NewLabel("") // Vertical spacer

	// Combine all elements with significantly improved spacing and visual hierarchy
	return container.NewVBox(
		welcomeText,
		spacer1,
		separator1,
		spacer2,
		statusHeader,
		statusContainer,
		spacer3,
		separator2,
		spacer4,
		actionsHeader,
		actionContainer,
		separator3,
		footerLabel,
	)
}

// createEnhancedStatusIndicator creates an enhanced status indicator with better styling
func createEnhancedStatusIndicator(name, status string, theme *cybertheme.CyberTheme) fyne.CanvasObject {
	// Create status label with enhanced styling
	statusLabel := widget.NewLabel(strings.ToUpper(status))
	statusLabel.TextStyle.Bold = true

	// Create name label
	nameLabel := widget.NewLabel(name)
	nameLabel.TextStyle.Bold = true

	// Create status icon based on status
	var statusIcon string
	switch status {
	case "online":
		statusIcon = "🟢"
	case "offline":
		statusIcon = "🔴"
	case "pending":
		statusIcon = "🟡"
	default:
		statusIcon = "⚪"
	}

	iconLabel := widget.NewLabel(statusIcon)

	return container.NewVBox(
		nameLabel,
		container.NewHBox(iconLabel, statusLabel),
	)
}

// createEnhancedButton creates an enhanced button with improved styling and text visibility
func createEnhancedButton(text string, tapped func(), theme *cybertheme.CyberTheme, primary bool) *widget.Button {
	button := widget.NewButton(text, tapped)

	// Set importance and sizing for better visibility
	if primary {
		button.Importance = widget.HighImportance
	} else {
		button.Importance = widget.MediumImportance
	}

	// Set minimum size for better touch targets and text visibility
	button.Resize(fyne.NewSize(180, 50))

	return button
}

// Content returns the main view's content
func (mv *MainView) Content() fyne.CanvasObject {
	return mv.container
}

// SetContent updates the main view's content
func (mv *MainView) SetContent(content fyne.CanvasObject) {
	// Clear existing content
	mv.container.Content = container.NewVBox()

	// Add new content to a scrollable container
	if content != nil {
		mv.container.Content = container.NewVBox(content)
		mv.content = content
	}

	// Refresh the container
	mv.container.Refresh()
}

// Refresh refreshes the main view
func (mv *MainView) Refresh() {
	if mv.container != nil {
		mv.container.Refresh()
	}
}

// GetCurrentContent returns the current content
func (mv *MainView) GetCurrentContent() fyne.CanvasObject {
	return mv.content
}

// SetMinSize sets the minimum size of the main view
func (mv *MainView) SetMinSize(size fyne.Size) {
	mv.container.SetMinSize(size)
}

// ShowLoading displays a loading indicator
func (mv *MainView) ShowLoading(message string) {
	loadingContent := container.NewVBox(
		widget.NewProgressBarInfinite(),
		widget.NewLabel(message),
		widget.NewLabel(">>> PROCESSING <<<"),
	)

	mv.SetContent(loadingContent)
}

// ShowError displays an error message with cyberpunk styling
func (mv *MainView) ShowError(title, message string) {
	errorContent := container.NewVBox(
		widget.NewLabel("🚨 ERROR DETECTED 🚨"),
		widget.NewSeparator(),
		widget.NewLabel("ERROR: "+title),
		widget.NewLabel(message),
		widget.NewSeparator(),
		widget.NewLabel(">>> SYSTEM ALERT <<<"),
	)

	mv.SetContent(errorContent)
}

// ShowSuccess displays a success message
func (mv *MainView) ShowSuccess(title, message string) {
	successContent := container.NewVBox(
		widget.NewLabel("✅ OPERATION SUCCESSFUL ✅"),
		widget.NewSeparator(),
		widget.NewLabel("SUCCESS: "+title),
		widget.NewLabel(message),
		widget.NewSeparator(),
		widget.NewLabel(">>> TASK COMPLETED <<<"),
	)

	mv.SetContent(successContent)
}
